use std::collections::HashMap;
use std::hash::{Hash, Hasher};

// ------------------ 数据结构定义 ------------------

#[derive(Debug, <PERSON><PERSON>, Eq)]
pub struct Sblot {
    pub customer: String,
    pub factory: String,
    pub device_id: String,
    pub test_area: String,
    pub lot_type: String,
    pub lot_id: String,
    pub test_stage: String,
    pub test_program: String,
}

#[derive(Debug, <PERSON><PERSON>, Eq)]
pub struct Lot {
    pub customer: String,
    pub factory: String,
    pub device_id: String,
    pub test_area: String,
    pub lot_type: String,
    pub lot_id: String,
    pub test_stage: String,
    pub test_program: String,
}

impl From<&Sblot> for Lot {
    fn from(sb: &Sblot) -> Self {
        Lot {
            customer: sb.customer.clone(),
            factory: sb.factory.clone(),
            device_id: sb.device_id.clone(),
            test_area: sb.test_area.clone(),
            lot_type: sb.lot_type.clone(),
            lot_id: sb.lot_id.clone(),
            test_stage: sb.test_stage.clone(),
            test_program: sb.test_program.clone(),
        }
    }
}

// 为 HashMap 做 Hash / PartialEq
impl Hash for Sblot {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.customer.hash(state);
        self.factory.hash(state);
        self.device_id.hash(state);
        self.test_area.hash(state);
        self.lot_type.hash(state);
        self.lot_id.hash(state);
        self.test_stage.hash(state);
        self.test_program.hash(state);
    }
}

impl PartialEq for Sblot {
    fn eq(&self, other: &Self) -> bool {
        self.customer == other.customer
            && self.factory == other.factory
            && self.device_id == other.device_id
            && self.test_area == other.test_area
            && self.lot_type == other.lot_type
            && self.lot_id == other.lot_id
            && self.test_stage == other.test_stage
            && self.test_program == other.test_program
    }
}

impl Hash for Lot {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.customer.hash(state);
        self.factory.hash(state);
        self.device_id.hash(state);
        self.test_area.hash(state);
        self.lot_type.hash(state);
        self.lot_id.hash(state);
        self.test_stage.hash(state);
        self.test_program.hash(state);
    }
}

impl PartialEq for Lot {
    fn eq(&self, other: &Self) -> bool {
        self.customer == other.customer
            && self.factory == other.factory
            && self.device_id == other.device_id
            && self.test_area == other.test_area
            && self.lot_type == other.lot_type
            && self.lot_id == other.lot_id
            && self.test_stage == other.test_stage
            && self.test_program == other.test_program
    }
}

pub type Bin = String;
pub type Count = i32;

// ------------------ 聚合函数 ------------------

/// 将sblot维度的数据聚合到lot维度
/// 对应Scala中的: sblotRetestBinNumMap.groupBy { case (sblot, _) => Lot(...) }.mapValues(...)
pub fn aggregate_sblot_to_lot(
    input: HashMap<Sblot, HashMap<Bin, Count>>,
) -> HashMap<Lot, HashMap<Bin, Count>> {
    let mut lot_map: HashMap<Lot, HashMap<Bin, Count>> = HashMap::new();

    for (sblot, bin_map) in input {
        let lot = Lot::from(&sblot);
        
        // 拿到或新建当前 lot 对应的 HashMap
        let lot_entry = lot_map.entry(lot).or_default();
        
        // 遍历每一个 (bin, count)
        for (bin, cnt) in bin_map {
            // 同一 bin 取最大值
            lot_entry
                .entry(bin)
                .and_modify(|e| *e = (*e).max(cnt))
                .or_insert(cnt);
        }
    }

    lot_map
}

/// 从DwsSubTestItemDetail构建Sblot
impl Sblot {
    pub fn from_test_item_detail(item: &crate::dws::dws_service::DwsSubTestItemDetail) -> Self {
        Sblot {
            customer: item.CUSTOMER.clone(),
            factory: item.FACTORY.clone(),
            device_id: item.DEVICE_ID.clone(),
            test_area: item.TEST_AREA.clone(),
            lot_type: item.LOT_TYPE.clone(),
            lot_id: item.LOT_ID.clone(),
            test_stage: item.TEST_STAGE.clone(),
            test_program: item.TEST_PROGRAM.clone(),
        }
    }
}

/// 从DwsSubTestItemDetail构建Lot
impl Lot {
    pub fn from_test_item_detail(item: &crate::dws::dws_service::DwsSubTestItemDetail) -> Self {
        Lot {
            customer: item.CUSTOMER.clone(),
            factory: item.FACTORY.clone(),
            device_id: item.DEVICE_ID.clone(),
            test_area: item.TEST_AREA.clone(),
            lot_type: item.LOT_TYPE.clone(),
            lot_id: item.LOT_ID.clone(),
            test_stage: item.TEST_STAGE.clone(),
            test_program: item.TEST_PROGRAM.clone(),
        }
    }
}

// ------------------ 测试 ------------------

#[cfg(test)]
mod tests {
    use super::*;

    fn make_sblot(id: u8) -> Sblot {
        Sblot {
            customer: format!("cust-{}", id),
            factory: format!("fab-{}", id),
            device_id: format!("dev-{}", id),
            test_area: format!("area-{}", id),
            lot_type: format!("type-{}", id),
            lot_id: format!("lot-{}", id),
            test_stage: format!("stage-{}", id),
            test_program: format!("prog-{}", id),
        }
    }

    #[test]
    fn test_aggregate_sblot_to_lot() {
        let mut input = HashMap::new();

        // 第一个sblot
        let s1 = make_sblot(1);
        let mut bin_map1 = HashMap::new();
        bin_map1.insert("bin1".to_string(), 10);
        bin_map1.insert("bin2".to_string(), 20);
        input.insert(s1.clone(), bin_map1);

        // 第二个sblot，与s1属于同一个lot
        let s2 = Sblot {
            lot_id: "lot-1".to_string(), // 与 s1 同 lot
            ..make_sblot(1) // 其他字段相同
        };
        let mut bin_map2 = HashMap::new();
        bin_map2.insert("bin2".to_string(), 5);   // 同 bin 取 max(20,5) = 20
        bin_map2.insert("bin3".to_string(), 99);  // 新 bin
        input.insert(s2, bin_map2);

        let result = aggregate_sblot_to_lot(input);

        let lot = Lot::from(&s1);
        let bins = result.get(&lot).unwrap();
        
        assert_eq!(bins[&"bin1".to_string()], 10);
        assert_eq!(bins[&"bin2".to_string()], 20); // 取最大值
        assert_eq!(bins[&"bin3".to_string()], 99);
    }

    #[test]
    fn test_different_lots() {
        let mut input = HashMap::new();

        // 第一个lot
        let s1 = make_sblot(1);
        let mut bin_map1 = HashMap::new();
        bin_map1.insert("bin1".to_string(), 10);
        input.insert(s1.clone(), bin_map1);

        // 第二个lot（不同的lot_id）
        let s2 = Sblot {
            lot_id: "lot-2".to_string(), // 不同的lot
            ..make_sblot(1)
        };
        let mut bin_map2 = HashMap::new();
        bin_map2.insert("bin1".to_string(), 5);
        input.insert(s2.clone(), bin_map2);

        let result = aggregate_sblot_to_lot(input);

        // 应该有两个不同的lot
        assert_eq!(result.len(), 2);
        
        let lot1 = Lot::from(&s1);
        let lot2 = Lot::from(&s2);
        
        assert_eq!(result[&lot1][&"bin1".to_string()], 10);
        assert_eq!(result[&lot2][&"bin1".to_string()], 5);
    }
}