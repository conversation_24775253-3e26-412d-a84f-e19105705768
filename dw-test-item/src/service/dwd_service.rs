use std::collections::HashMap;
use std::error::Error;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Semaphore;
use ck_provider::{AsyncCkChannel, CkProviderImpl, CkStreamProcessorBuilder, StreamConfig, StreamMetrics};
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::dwd::test_item_detail_row::TestItemDetailRow;
use common::dwd::sink::test_item_detail_handler::TestItemDetailHandler;
use common::dwd::table::test_item_detail_common_service::TestItemDetailCommonService;
use crate::config::DwTestItemConfig;
use crate::error::DatawareError::DwdCalculateFailed;

pub async fn write_test_item_detail_to_clickhouse_concurrent(
    properties: DwTestItemConfig,
    test_item_detail: Vec<Vec<SubTestItemDetail>>,
    file_detail_map: HashMap<u64, FileDetail>,
    config: &DwTestItemConfig,
) -> Result<(Vec<Vec<SubTestItemDetail>>, HashMap<u64, FileDetail>), Box<dyn Error + Send + Sync>> {
    let ck_config = properties.get_ck_config(properties.dwd_db_name.as_str());
    let test_item_detail_handler =
        Arc::new(TestItemDetailHandler::new(properties.dwd_db_name.clone(), properties.insert_cluster_table));

    let db_table_name = format!("{}.{}", test_item_detail_handler.db_name, test_item_detail_handler.table_name);
    let batch_size = config.get_batch_size()?;

    // 配置流式处理参数
    let stream_config = StreamConfig::default()
        .with_buffer_size(batch_size * 2)
        .with_batch_size(batch_size)
        .with_flush_interval(Duration::from_secs(2))
        .with_max_retries(3)
        .with_backpressure_timeout(Duration::from_secs(600))
        .with_parallel_flush(true)
        .with_max_concurrent_flushes(test_item_detail.len() * 2);

    // 创建指标收集器
    let metrics = Arc::new(StreamMetrics::new());

    // 创建流式通道
    let (sender, receiver) = AsyncCkChannel::new::<TestItemDetailRow>(stream_config.clone(), metrics.clone());

    let ck_provider = CkProviderImpl::new(ck_config.clone());

    // 创建流处理器
    let mut processor = CkStreamProcessorBuilder::new()
        .with_receiver(receiver)
        .with_provider(ck_provider.clone())
        .with_metrics(metrics.clone())
        .with_config(stream_config)
        .with_table_name(db_table_name)
        .build()?;

    // 启动流处理器任务
    let processor_handle = tokio::spawn(async move {
        if let Err(e) = processor.start().await {
            eprintln!("流处理器错误: {:?}", e);
        }
    });

    // 直接在这里执行并发写入，避免数据传递和克隆
    let file_detail_map_arc = Arc::new(file_detail_map);
    let test_item_detail_arc = Arc::new(test_item_detail);

    // 限制并发数，避免创建过多任务导致系统过载
    let max_concurrent_batches = std::cmp::min(test_item_detail_arc.len(), 8);
    let semaphore = Arc::new(Semaphore::new(max_concurrent_batches));

    // 创建并发任务处理每个批次
    let mut tasks = Vec::new();
    for batch_idx in 0..test_item_detail_arc.len() {
        let sender_clone = sender.clone();
        let file_detail_map_clone = file_detail_map_arc.clone();
        let test_item_detail_clone = test_item_detail_arc.clone();
        let semaphore_clone = semaphore.clone();

        let task = tokio::spawn(async move {
            // 获取信号量许可
            let _permit = semaphore_clone.acquire().await.unwrap();

            let test_item_detail_batch = &test_item_detail_clone[batch_idx];
            log::info!("开始处理批次{}写入ClickHouse，数据量: {}", batch_idx, test_item_detail_batch.len());

            // 处理批次中的每个项目
            for item in test_item_detail_batch.iter() {
                let file_detail = file_detail_map_clone.get(&(item.FILE_ID.unwrap() as u64)).unwrap();
                let test_item_detail_row = TestItemDetailCommonService::get_test_item_detail_with_file_info(
                    item,
                    &file_detail,
                )
                    .map_err(|e| -> Box<dyn Error + Send + Sync> {
                        Box::new(DwdCalculateFailed(format!("批次{}计算test_item_detail_row失败: {}", batch_idx, e)))
                    })?;

                sender_clone.send(Some(test_item_detail_row)).await?;
            }

            log::info!("完成批次{}写入ClickHouse", batch_idx);
            Ok::<(), Box<dyn Error + Send + Sync>>(())
        });
        tasks.push(task);
    }

    // 等待所有任务完成
    for task in tasks {
        task.await??;
    }

    // 发送结束消息
    sender.send(None).await?;

    // 等待处理器完成
    processor_handle.await?;

    // 输出最终指标
    log::info!("ClickHouse写入最终指标:\n {}", metrics.snapshot().format_summary());

    // 从Arc中提取原始数据返回
    let file_detail_map = Arc::try_unwrap(file_detail_map_arc).map_err(|_| "Failed to unwrap Arc<HashMap>")?;
    let test_item_detail =
        Arc::try_unwrap(test_item_detail_arc).map_err(|_| "Failed to unwrap Arc<Vec<Vec<SubTestItemDetail>>>")?;

    Ok((test_item_detail, file_detail_map))
}
